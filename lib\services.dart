// ملف الخدمات (Services) - يحتوي على جميع الخدمات والمستودعات المستخدمة في التطبيق

import 'dart:io';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'models.dart';

// ======================================================
// خدمة المصادقة (Authentication Service)
// ======================================================

class AuthService {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // الحصول على المستخدم الحالي
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();

  // تسجيل الدخول باستخدام البريد الإلكتروني وكلمة المرور
  Future<UserModel> signInWithEmailAndPassword(
      String email, String password) async {
    try {
      final userCredential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (userCredential.user != null) {
        final userDoc = await _firestore
            .collection('users')
            .doc(userCredential.user!.uid)
            .get();
        if (userDoc.exists) {
          return UserModel.fromJson({
            'id': userCredential.user!.uid,
            ...userDoc.data()!,
          });
        } else {
          // إنشاء وثيقة المستخدم إذا لم تكن موجودة
          final newUser = UserModel(
            id: userCredential.user!.uid,
            email: email,
            name: userCredential.user!.displayName ?? email.split('@')[0],
          );
          await _firestore
              .collection('users')
              .doc(newUser.id)
              .set(newUser.toJson());
          return newUser;
        }
      } else {
        throw Exception('فشل تسجيل الدخول');
      }
    } on FirebaseAuthException catch (e) {
      if (e.code == 'user-not-found') {
        throw Exception('لم يتم العثور على مستخدم بهذا البريد الإلكتروني');
      } else if (e.code == 'wrong-password') {
        throw Exception('كلمة المرور غير صحيحة');
      } else {
        throw Exception('فشل تسجيل الدخول: ${e.message}');
      }
    } catch (e) {
      throw Exception('فشل تسجيل الدخول: $e');
    }
  }

  // إنشاء حساب جديد باستخدام البريد الإلكتروني وكلمة المرور
  Future<UserModel> signUpWithEmailAndPassword(
    String email,
    String password,
    String name,
    String? phoneNumber,
    String? userType,
  ) async {
    try {
      final userCredential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (userCredential.user != null) {
        // تحديث اسم المستخدم في Firebase Auth
        await userCredential.user!.updateDisplayName(name);

        // إنشاء وثيقة المستخدم في Firestore
        final newUser = UserModel(
          id: userCredential.user!.uid,
          email: email,
          name: name,
          phoneNumber: phoneNumber,
          userType: userType ?? 'مزارع', // القيمة الافتراضية هي "مزارع"
        );

        await _firestore
            .collection('users')
            .doc(newUser.id)
            .set(newUser.toJson());
        return newUser;
      } else {
        throw Exception('فشل إنشاء الحساب');
      }
    } on FirebaseAuthException catch (e) {
      if (e.code == 'weak-password') {
        throw Exception('كلمة المرور ضعيفة جدًا');
      } else if (e.code == 'email-already-in-use') {
        throw Exception('البريد الإلكتروني مستخدم بالفعل');
      } else {
        throw Exception('فشل إنشاء الحساب: ${e.message}');
      }
    } catch (e) {
      throw Exception('فشل إنشاء الحساب: $e');
    }
  }

  // تسجيل الخروج
  Future<void> signOut() async {
    await _firebaseAuth.signOut();
  }

  // إعادة تعيين كلمة المرور
  Future<void> resetPassword(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      if (e.code == 'user-not-found') {
        throw Exception('لم يتم العثور على مستخدم بهذا البريد الإلكتروني');
      } else {
        throw Exception('فشل إعادة تعيين كلمة المرور: ${e.message}');
      }
    } catch (e) {
      throw Exception('فشل إعادة تعيين كلمة المرور: $e');
    }
  }

  // الحصول على بيانات المستخدم الحالي
  Future<UserModel?> getCurrentUser() async {
    final user = _firebaseAuth.currentUser;
    if (user != null) {
      final userDoc = await _firestore.collection('users').doc(user.uid).get();
      if (userDoc.exists) {
        return UserModel.fromJson({
          'id': user.uid,
          ...userDoc.data()!,
        });
      }
    }
    return null;
  }

  // تحديث بيانات المستخدم
  Future<UserModel> updateUserProfile(UserModel user,
      {File? profileImage}) async {
    try {
      // تحميل صورة الملف الشخصي إذا تم توفيرها
      String? photoUrl = user.photoUrl;
      if (profileImage != null) {
        final storageRef = _storage.ref().child('profile_images/${user.id}');
        final uploadTask = storageRef.putFile(profileImage);
        final snapshot = await uploadTask;
        photoUrl = await snapshot.ref.getDownloadURL();
      }

      // تحديث بيانات المستخدم في Firestore
      final updatedUser = user.copyWith(photoUrl: photoUrl);
      await _firestore
          .collection('users')
          .doc(user.id)
          .update(updatedUser.toJson());

      // تحديث اسم المستخدم في Firebase Auth
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser != null) {
        await currentUser.updateDisplayName(user.name);
        if (photoUrl != null) {
          await currentUser.updatePhotoURL(photoUrl);
        }
      }

      return updatedUser;
    } catch (e) {
      throw Exception('فشل تحديث الملف الشخصي: $e');
    }
  }
}

// ======================================================
// خدمة نظام الري (Irrigation Service)
// ======================================================

class IrrigationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String userId;

  IrrigationService({required this.userId});

  // الحصول على جميع أنظمة الري للمستخدم الحالي
  Stream<List<IrrigationSystemModel>> getIrrigationSystems() {
    return _firestore
        .collection('irrigation_systems')
        .where('userId', isEqualTo: userId)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return IrrigationSystemModel.fromJson({
          'id': doc.id,
          ...doc.data(),
        });
      }).toList();
    });
  }

  // الحصول على نظام ري محدد
  Stream<IrrigationSystemModel> getIrrigationSystem(String systemId) {
    return _firestore
        .collection('irrigation_systems')
        .doc(systemId)
        .snapshots()
        .map((doc) {
      return IrrigationSystemModel.fromJson({
        'id': doc.id,
        ...doc.data()!,
      });
    });
  }

  // إضافة نظام ري جديد
  Future<IrrigationSystemModel> addIrrigationSystem(
      IrrigationSystemModel system) async {
    try {
      final docRef = await _firestore
          .collection('irrigation_systems')
          .add(system.toJson());
      return system.copyWith(id: docRef.id);
    } catch (e) {
      throw Exception('فشل إضافة نظام الري: $e');
    }
  }

  // تحديث نظام ري
  Future<void> updateIrrigationSystem(IrrigationSystemModel system) async {
    try {
      await _firestore
          .collection('irrigation_systems')
          .doc(system.id)
          .update(system.toJson());
    } catch (e) {
      throw Exception('فشل تحديث نظام الري: $e');
    }
  }

  // حذف نظام ري
  Future<void> deleteIrrigationSystem(String systemId) async {
    try {
      await _firestore.collection('irrigation_systems').doc(systemId).delete();
    } catch (e) {
      throw Exception('فشل حذف نظام الري: $e');
    }
  }

  // تشغيل/إيقاف المضخة
  Future<void> togglePump(String systemId, bool isPumpOn) async {
    try {
      await _firestore.collection('irrigation_systems').doc(systemId).update({
        'isPumpOn': isPumpOn,
      });

      // هنا يمكن إضافة كود للتواصل مع ESP32 لتشغيل/إيقاف المضخة فعليًا
      // على سبيل المثال، يمكن استخدام Firebase Realtime Database للتواصل مع ESP32
    } catch (e) {
      throw Exception('فشل تشغيل/إيقاف المضخة: $e');
    }
  }

  // تفعيل/تعطيل الوضع التلقائي
  Future<void> toggleAutomaticMode(String systemId, bool isAutomatic) async {
    try {
      await _firestore.collection('irrigation_systems').doc(systemId).update({
        'isAutomatic': isAutomatic,
      });
    } catch (e) {
      throw Exception('فشل تفعيل/تعطيل الوضع التلقائي: $e');
    }
  }

  // تحديث بيانات الحساسات
  Future<void> updateSensorData(
      String systemId, SensorDataModel sensorData) async {
    try {
      await _firestore.collection('irrigation_systems').doc(systemId).update({
        'sensorData': sensorData.toJson(),
      });

      // التحقق من الوضع التلقائي وتشغيل المضخة إذا لزم الأمر
      final systemDoc =
          await _firestore.collection('irrigation_systems').doc(systemId).get();
      if (systemDoc.exists) {
        final system = IrrigationSystemModel.fromJson({
          'id': systemDoc.id,
          ...systemDoc.data()!,
        });

        if (system.isAutomatic &&
            sensorData.soilMoisture < system.moistureThreshold &&
            !system.isPumpOn) {
          // تشغيل المضخة تلقائيًا
          await togglePump(systemId, true);

          // جدولة إيقاف المضخة بعد المدة المحددة
          Future.delayed(Duration(minutes: system.wateringDurationMinutes), () {
            togglePump(systemId, false);
          });
        }
      }
    } catch (e) {
      throw Exception('فشل تحديث بيانات الحساسات: $e');
    }
  }
}

// ======================================================
// خدمة تشخيص أمراض النباتات (Disease Diagnosis Service)
// ======================================================

class DiseaseDiagnosisService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final String userId;

  DiseaseDiagnosisService({required this.userId});

  // تشخيص مرض النبات باستخدام صورة
  Future<DiseaseDiagnosisModel> diagnosePlantDisease(File imageFile) async {
    try {
      // تحميل الصورة إلى Firebase Storage
      final uuid = Uuid();
      final imageId = uuid.v4();
      final storageRef = _storage.ref().child('plant_images/$imageId');
      final uploadTask = storageRef.putFile(imageFile);
      final snapshot = await uploadTask;
      final imageUrl = await snapshot.ref.getDownloadURL();

      // هنا يمكن إضافة كود لاستدعاء نموذج الذكاء الاصطناعي لتشخيص المرض
      // في هذا المثال، سنستخدم بيانات وهمية للتوضيح

      // محاكاة تأخير لعملية التشخيص
      await Future.delayed(Duration(seconds: 2));

      // بيانات تشخيص وهمية (في التطبيق الحقيقي، ستأتي من نموذج الذكاء الاصطناعي)
      final diagnosisId = uuid.v4();
      final diagnosis = DiseaseDiagnosisModel(
        id: diagnosisId,
        userId: userId,
        diseaseName: 'اللفحة المتأخرة',
        confidence: 0.92,
        recommendations:
            'يوصى برش مبيد فطري مناسب مثل مانكوزيب أو كلوروثالونيل. قم بإزالة الأوراق المصابة وتجنب الري العلوي.',
        imageUrl: imageUrl,
        timestamp: Timestamp.now(),
      );

      // حفظ نتيجة التشخيص في Firestore
      await _firestore
          .collection('disease_diagnoses')
          .doc(diagnosisId)
          .set(diagnosis.toJson());

      return diagnosis;
    } catch (e) {
      throw Exception('فشل تشخيص مرض النبات: $e');
    }
  }

  // الحصول على سجل تشخيصات المستخدم
  Stream<List<DiseaseDiagnosisModel>> getDiagnosisHistory() {
    return _firestore
        .collection('disease_diagnoses')
        .where('userId', isEqualTo: userId)
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return DiseaseDiagnosisModel.fromJson({
          'id': doc.id,
          ...doc.data(),
        });
      }).toList();
    });
  }
}

// ======================================================
// خدمة السوق الزراعي (Market Service)
// ======================================================

class MarketService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  final String userId;

  MarketService({required this.userId});

  // الحصول على جميع المنتجات
  Stream<List<ProductModel>> getProducts(
      {String? category, String? searchQuery}) {
    Query query = _firestore
        .collection('products')
        .orderBy('timestamp', descending: true);

    if (category != null && category.isNotEmpty && category != 'الكل') {
      query = query.where('category', isEqualTo: category);
    }

    return query.snapshots().map((snapshot) {
      List<ProductModel> products = snapshot.docs.map((doc) {
        return ProductModel.fromJson({
          'id': doc.id,
          ...doc.data(),
        });
      }).toList();

      // تصفية المنتجات حسب البحث إذا تم توفير استعلام البحث
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final searchLower = searchQuery.toLowerCase();
        products = products.where((product) {
          return product.name.toLowerCase().contains(searchLower) ||
              (product.description?.toLowerCase().contains(searchLower) ??
                  false);
        }).toList();
      }

      return products;
    });
  }

  // الحصول على منتج محدد
  Future<ProductModel> getProduct(String productId) async {
    try {
      final doc = await _firestore.collection('products').doc(productId).get();
      if (doc.exists) {
        return ProductModel.fromJson({
          'id': doc.id,
          ...doc.data()!,
        });
      } else {
        throw Exception('المنتج غير موجود');
      }
    } catch (e) {
      throw Exception('فشل الحصول على المنتج: $e');
    }
  }

  // إضافة منتج جديد
  Future<ProductModel> addProduct(
    String name,
    double price,
    String? description,
    String? category,
    File? imageFile,
    String? location,
  ) async {
    try {
      // الحصول على بيانات المستخدم
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final userData = userDoc.data();

      // تحميل صورة المنتج إذا تم توفيرها
      String? imageUrl;
      if (imageFile != null) {
        final uuid = Uuid();
        final imageId = uuid.v4();
        final storageRef = _storage.ref().child('product_images/$imageId');
        final uploadTask = storageRef.putFile(imageFile);
        final snapshot = await uploadTask;
        imageUrl = await snapshot.ref.getDownloadURL();
      }

      // إنشاء المنتج
      final productId = Uuid().v4();
      final product = ProductModel(
        id: productId,
        name: name,
        price: price,
        description: description,
        category: category,
        imageUrl: imageUrl,
        sellerId: userId,
        sellerName: userData?['name'],
        sellerPhotoUrl: userData?['photoUrl'],
        location: location,
        timestamp: Timestamp.now(),
      );

      // حفظ المنتج في Firestore
      await _firestore
          .collection('products')
          .doc(productId)
          .set(product.toJson());

      return product;
    } catch (e) {
      throw Exception('فشل إضافة المنتج: $e');
    }
  }

  // تحديث منتج
  Future<void> updateProduct(ProductModel product, {File? imageFile}) async {
    try {
      // تحميل صورة المنتج الجديدة إذا تم توفيرها
      String? imageUrl = product.imageUrl;
      if (imageFile != null) {
        final uuid = Uuid();
        final imageId = uuid.v4();
        final storageRef = _storage.ref().child('product_images/$imageId');
        final uploadTask = storageRef.putFile(imageFile);
        final snapshot = await uploadTask;
        imageUrl = await snapshot.ref.getDownloadURL();
      }

      // تحديث المنتج في Firestore
      final updatedProduct = ProductModel(
        id: product.id,
        name: product.name,
        price: product.price,
        description: product.description,
        category: product.category,
        imageUrl: imageUrl,
        sellerId: product.sellerId,
        sellerName: product.sellerName,
        sellerPhotoUrl: product.sellerPhotoUrl,
        location: product.location,
        timestamp: product.timestamp,
      );

      await _firestore
          .collection('products')
          .doc(product.id)
          .update(updatedProduct.toJson());
    } catch (e) {
      throw Exception('فشل تحديث المنتج: $e');
    }
  }

  // حذف منتج
  Future<void> deleteProduct(String productId) async {
    try {
      await _firestore.collection('products').doc(productId).delete();
    } catch (e) {
      throw Exception('فشل حذف المنتج: $e');
    }
  }

  // الحصول على منتجات المستخدم
  Stream<List<ProductModel>> getUserProducts() {
    return _firestore
        .collection('products')
        .where('sellerId', isEqualTo: userId)
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return ProductModel.fromJson({
          'id': doc.id,
          ...doc.data(),
        });
      }).toList();
    });
  }
}

// ======================================================
// خدمة المحادثات (Chat Service)
// ======================================================

class ChatService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String userId;

  ChatService({required this.userId});

  // الحصول على جميع المحادثات للمستخدم الحالي
  Stream<List<ChatModel>> getChats() {
    return _firestore
        .collection('chats')
        .where('participants', arrayContains: userId)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return ChatModel.fromJson({
          'id': doc.id,
          ...doc.data(),
        });
      }).toList();
    });
  }

  // الحصول على محادثة محددة أو إنشاء محادثة جديدة إذا لم تكن موجودة
  Future<String> getOrCreateChatWithUser(String otherUserId) async {
    try {
      // البحث عن محادثة موجودة بين المستخدمين
      final querySnapshot = await _firestore
          .collection('chats')
          .where('participants', arrayContains: userId)
          .get();

      for (final doc in querySnapshot.docs) {
        final chat = ChatModel.fromJson({
          'id': doc.id,
          ...doc.data(),
        });

        if (chat.participants.contains(otherUserId)) {
          return chat.id;
        }
      }

      // إذا لم يتم العثور على محادثة، قم بإنشاء محادثة جديدة
      // الحصول على بيانات المستخدمين
      final currentUserDoc =
          await _firestore.collection('users').doc(userId).get();
      final otherUserDoc =
          await _firestore.collection('users').doc(otherUserId).get();

      if (!currentUserDoc.exists || !otherUserDoc.exists) {
        throw Exception('أحد المستخدمين غير موجود');
      }

      final currentUserData = currentUserDoc.data()!;
      final otherUserData = otherUserDoc.data()!;

      // إنشاء محادثة جديدة
      final chatId = Uuid().v4();
      final chat = ChatModel(
        id: chatId,
        participants: [userId, otherUserId],
        participantInfo: {
          userId: {
            'name': currentUserData['name'],
            'photoUrl': currentUserData['photoUrl'],
          },
          otherUserId: {
            'name': otherUserData['name'],
            'photoUrl': otherUserData['photoUrl'],
          },
        },
      );

      await _firestore.collection('chats').doc(chatId).set(chat.toJson());

      return chatId;
    } catch (e) {
      throw Exception('فشل الحصول على/إنشاء محادثة: $e');
    }
  }

  // الحصول على رسائل محادثة محددة
  Stream<List<MessageModel>> getMessages(String chatId) {
    return _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return MessageModel.fromJson({
          'id': doc.id,
          ...doc.data(),
        });
      }).toList();
    });
  }

  // إرسال رسالة
  Future<void> sendMessage(String chatId, String text) async {
    try {
      // الحصول على بيانات المستخدم
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final userData = userDoc.data();

      if (userData == null) {
        throw Exception('بيانات المستخدم غير موجودة');
      }

      // إنشاء الرسالة
      final messageId = Uuid().v4();
      final message = MessageModel(
        id: messageId,
        chatId: chatId,
        senderId: userId,
        senderName: userData['name'],
        text: text,
        timestamp: Timestamp.now(),
      );

      // حفظ الرسالة في Firestore
      await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .set(message.toJson());

      // تحديث آخر رسالة في المحادثة
      await _firestore.collection('chats').doc(chatId).update({
        'lastMessage': text,
        'lastMessageTimestamp': message.timestamp,
      });
    } catch (e) {
      throw Exception('فشل إرسال الرسالة: $e');
    }
  }
}

// ======================================================
// خدمة الإعدادات (Settings Service)
// ======================================================

class SettingsService {
  static const String _themeKey = 'theme_mode';
  static const String _notificationsKey = 'notifications_enabled';
  static const String _languageKey = 'language';

  // الحصول على وضع السمة المحفوظ
  Future<ThemeMode> getThemeMode() async {
    final prefs = await SharedPreferences.getInstance();
    final themeIndex = prefs.getInt(_themeKey) ?? 0;
    return ThemeMode.values[themeIndex];
  }

  // حفظ وضع السمة
  Future<void> setThemeMode(ThemeMode themeMode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_themeKey, themeMode.index);
  }

  // الحصول على حالة الإشعارات
  Future<bool> getNotificationsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_notificationsKey) ?? true;
  }

  // تعيين حالة الإشعارات
  Future<void> setNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_notificationsKey, enabled);
  }

  // الحصول على اللغة المحفوظة
  Future<String> getLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_languageKey) ?? 'ar';
  }

  // حفظ اللغة
  Future<void> setLanguage(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_languageKey, languageCode);
  }
}

// ======================================================
// خدمة ESP32 (ESP32 Service)
// ======================================================

class ESP32Service {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // إرسال أمر إلى ESP32
  Future<void> sendCommandToESP32(
      String espId, String command, dynamic value) async {
    try {
      await _firestore.collection('esp32_commands').add({
        'espId': espId,
        'command': command,
        'value': value,
        'timestamp': Timestamp.now(),
        'executed': false,
      });
    } catch (e) {
      throw Exception('فشل إرسال الأمر إلى ESP32: $e');
    }
  }

  // الحصول على آخر بيانات من ESP32
  Stream<Map<String, dynamic>> getESP32Data(String espId) {
    return _firestore
        .collection('esp32_data')
        .where('espId', isEqualTo: espId)
        .orderBy('timestamp', descending: true)
        .limit(1)
        .snapshots()
        .map((snapshot) {
      if (snapshot.docs.isNotEmpty) {
        return snapshot.docs.first.data();
      } else {
        return {};
      }
    });
  }
}

// ======================================================
// خدمة الصور (Image Service)
// ======================================================

class ImageService {
  final ImagePicker _imagePicker = ImagePicker();

  // التقاط صورة من الكاميرا
  Future<File?> captureImageFromCamera() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
      );

      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      throw Exception('فشل التقاط الصورة: $e');
    }
  }

  // اختيار صورة من المعرض
  Future<File?> pickImageFromGallery() async {
    try {
      final pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (pickedFile != null) {
        return File(pickedFile.path);
      }
      return null;
    } catch (e) {
      throw Exception('فشل اختيار الصورة: $e');
    }
  }
}
