// ملف BLoCs - يحتوي على جميع BLoCs المستخدمة في التطبيق

import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'models.dart';
import 'services.dart';
import 'repositories.dart';

// ======================================================
// BLoC المصادقة (Authentication BLoC)
// ======================================================

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthService _authService;
  final UserRepository _userRepository;

  AuthBloc(this._authService, this._userRepository) : super(const AuthState()) {
    on<AuthCheckStatus>(_onCheckStatus);
    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthRegisterRequested>(_onRegisterRequested);
    on<AuthLogoutRequested>(_onLogoutRequested);
    on<AuthForgotPasswordRequested>(_onForgotPasswordRequested);
    on<AuthUserUpdated>(_onUserUpdated);
  }

  Future<void> _onCheckStatus(AuthCheckStatus event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.loading));
    try {
      final user = await _authService.getCurrentUser();
      if (user != null) {
        emit(state.copyWith(status: AuthStatus.authenticated, user: user));
      } else {
        emit(state.copyWith(status: AuthStatus.unauthenticated));
      }
    } catch (e) {
      emit(state.copyWith(status: AuthStatus.error, errorMessage: e.toString()));
    }
  }

  Future<void> _onLoginRequested(AuthLoginRequested event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.loading));
    try {
      final user = await _authService.signInWithEmailAndPassword(event.email, event.password);
      emit(state.copyWith(status: AuthStatus.authenticated, user: user));
    } catch (e) {
      emit(state.copyWith(status: AuthStatus.error, errorMessage: e.toString()));
    }
  }

  Future<void> _onRegisterRequested(AuthRegisterRequested event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.loading));
    try {
      final user = await _authService.signUpWithEmailAndPassword(
        event.email,
        event.password,
        event.name,
        event.phoneNumber,
        event.userType,
      );
      emit(state.copyWith(status: AuthStatus.authenticated, user: user));
    } catch (e) {
      emit(state.copyWith(status: AuthStatus.error, errorMessage: e.toString()));
    }
  }

  Future<void> _onLogoutRequested(AuthLogoutRequested event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.loading));
    try {
      await _authService.signOut();
      emit(state.copyWith(status: AuthStatus.unauthenticated, user: null));
    } catch (e) {
      emit(state.copyWith(status: AuthStatus.error, errorMessage: e.toString()));
    }
  }

  Future<void> _onForgotPasswordRequested(AuthForgotPasswordRequested event, Emitter<AuthState> emit) async {
    emit(state.copyWith(status: AuthStatus.loading));
    try {
      await _authService.resetPassword(event.email);
      emit(state.copyWith(status: AuthStatus.unauthenticated));
    } catch (e) {
      emit(state.copyWith(status: AuthStatus.error, errorMessage: e.toString()));
    }
  }

  Future<void> _onUserUpdated(AuthUserUpdated event, Emitter<AuthState> emit) async {
    emit(state.copyWith(user: event.user));
  }
}

// ======================================================
// BLoC أنظمة الري (Irrigation BLoC)
// ======================================================

class IrrigationBloc extends Bloc<IrrigationEvent, IrrigationState> {
  final IrrigationSystemRepository _repository;
  final IrrigationService _service;

  IrrigationBloc(this._repository, this._service) : super(IrrigationInitial()) {
    on<LoadIrrigationSystems>(_onLoadSystems);
    on<LoadIrrigationSystem>(_onLoadSystem);
    on<AddIrrigationSystem>(_onAddSystem);
    on<UpdateIrrigationSystem>(_onUpdateSystem);
    on<DeleteIrrigationSystem>(_onDeleteSystem);
    on<TogglePump>(_onTogglePump);
    on<ToggleAutomaticMode>(_onToggleAutomaticMode);
  }

  Future<void> _onLoadSystems(LoadIrrigationSystems event, Emitter<IrrigationState> emit) async {
    emit(IrrigationLoading());
    try {
      await emit.forEach(
        _repository.getIrrigationSystems(_service.userId),
        onData: (systems) => IrrigationSystemsLoaded(systems),
        onError: (error, stackTrace) => IrrigationError(error.toString()),
      );
    } catch (e) {
      emit(IrrigationError(e.toString()));
    }
  }

  Future<void> _onLoadSystem(LoadIrrigationSystem event, Emitter<IrrigationState> emit) async {
    emit(IrrigationLoading());
    try {
      await emit.forEach(
        _repository.getIrrigationSystem(event.systemId),
        onData: (system) => IrrigationSystemLoaded(system),
        onError: (error, stackTrace) => IrrigationError(error.toString()),
      );
    } catch (e) {
      emit(IrrigationError(e.toString()));
    }
  }

  Future<void> _onAddSystem(AddIrrigationSystem event, Emitter<IrrigationState> emit) async {
    emit(IrrigationLoading());
    try {
      final systemId = await _repository.addIrrigationSystem(event.system);
      final newSystem = event.system.copyWith(id: systemId);
      emit(IrrigationSystemAdded(newSystem));
    } catch (e) {
      emit(IrrigationError(e.toString()));
    }
  }

  Future<void> _onUpdateSystem(UpdateIrrigationSystem event, Emitter<IrrigationState> emit) async {
    emit(IrrigationLoading());
    try {
      await _repository.updateIrrigationSystem(event.system);
      emit(IrrigationSystemUpdated(event.system));
    } catch (e) {
      emit(IrrigationError(e.toString()));
    }
  }

  Future<void> _onDeleteSystem(DeleteIrrigationSystem event, Emitter<IrrigationState> emit) async {
    emit(IrrigationLoading());
    try {
      await _repository.deleteIrrigationSystem(event.systemId);
      emit(IrrigationSystemDeleted(event.systemId));
    } catch (e) {
      emit(IrrigationError(e.toString()));
    }
  }

  Future<void> _onTogglePump(TogglePump event, Emitter<IrrigationState> emit) async {
    try {
      await _service.togglePump(event.systemId, event.isPumpOn);
    } catch (e) {
      emit(IrrigationError(e.toString()));
    }
  }

  Future<void> _onToggleAutomaticMode(ToggleAutomaticMode event, Emitter<IrrigationState> emit) async {
    try {
      await _service.toggleAutomaticMode(event.systemId, event.isAutomatic);
    } catch (e) {
      emit(IrrigationError(e.toString()));
    }
  }
}

// ======================================================
// BLoC تشخيص الأمراض (Disease Diagnosis BLoC)
// ======================================================

class DiseaseDiagnosisBloc extends Bloc<DiseaseDiagnosisEvent, DiseaseDiagnosisState> {
  final DiseaseDiagnosisService _service;

  DiseaseDiagnosisBloc(this._service) : super(DiseaseDiagnosisInitial()) {
    on<DiagnosePlantDisease>(_onDiagnosePlantDisease);
  }

  Future<void> _onDiagnosePlantDisease(DiagnosePlantDisease event, Emitter<DiseaseDiagnosisState> emit) async {
    emit(DiseaseDiagnosisLoading());
    try {
      final diagnosis = await _service.diagnosePlantDisease(event.imageFile);
      emit(DiseaseDiagnosisSuccess(diagnosis));
    } catch (e) {
      emit(DiseaseDiagnosisError(e.toString()));
    }
  }
}

// ======================================================
// BLoC السوق (Market BLoC)
// ======================================================

class MarketBloc extends Bloc<MarketEvent, MarketState> {
  final MarketRepository _repository;

  MarketBloc(this._repository) : super(MarketInitial()) {
    on<LoadProducts>(_onLoadProducts);
    on<LoadProduct>(_onLoadProduct);
    on<AddProduct>(_onAddProduct);
    on<UpdateProduct>(_onUpdateProduct);
    on<DeleteProduct>(_onDeleteProduct);
  }

  Future<void> _onLoadProducts(LoadProducts event, Emitter<MarketState> emit) async {
    emit(MarketLoading());
    try {
      await emit.forEach(
        _repository.getProducts(category: event.category, searchQuery: event.searchQuery),
        onData: (products) => ProductsLoaded(products),
        onError: (error, stackTrace) => MarketError(error.toString()),
      );
    } catch (e) {
      emit(MarketError(e.toString()));
    }
  }

  Future<void> _onLoadProduct(LoadProduct event, Emitter<MarketState> emit) async {
    emit(MarketLoading());
    try {
      final product = await _repository.getProduct(event.productId);
      emit(ProductLoaded(product));
    } catch (e) {
      emit(MarketError(e.toString()));
    }
  }

  Future<void> _onAddProduct(AddProduct event, Emitter<MarketState> emit) async {
    emit(MarketLoading());
    try {
      final productId = await _repository.addProduct(event.product);
      final newProduct = event.product.copyWith(id: productId);
      emit(ProductAdded(newProduct));
    } catch (e) {
      emit(MarketError(e.toString()));
    }
  }

  Future<void> _onUpdateProduct(UpdateProduct event, Emitter<MarketState> emit) async {
    emit(MarketLoading());
    try {
      await _repository.updateProduct(event.product);
      emit(ProductUpdated(event.product));
    } catch (e) {
      emit(MarketError(e.toString()));
    }
  }

  Future<void> _onDeleteProduct(DeleteProduct event, Emitter<MarketState> emit) async {
    emit(MarketLoading());
    try {
      await _repository.deleteProduct(event.productId);
      emit(ProductDeleted(event.productId));
    } catch (e) {
      emit(MarketError(e.toString()));
    }
  }
}

// ======================================================
// BLoC المحادثات (Chat BLoC)
// ======================================================

class ChatBloc extends Bloc<ChatEvent, ChatState> {
  final ChatRepository _repository;

  ChatBloc(this._repository) : super(ChatInitial()) {
    on<LoadChats>(_onLoadChats);
    on<LoadOrCreateChatWithUser>(_onLoadOrCreateChatWithUser);
    on<LoadMessages>(_onLoadMessages);
    on<SendMessage>(_onSendMessage);
  }

  Future<void> _onLoadChats(LoadChats event, Emitter<ChatState> emit) async {
    emit(ChatLoading());
    try {
      // يحتاج إلى userId من مكان ما
      // await emit.forEach(
      //   _repository.getChats(userId),
      //   onData: (chats) => ChatsLoaded(chats),
      //   onError: (error, stackTrace) => ChatError(error.toString()),
      // );
    } catch (e) {
      emit(ChatError(e.toString()));
    }
  }

  Future<void> _onLoadOrCreateChatWithUser(LoadOrCreateChatWithUser event, Emitter<ChatState> emit) async {
    emit(ChatLoading());
    try {
      // منطق إنشاء أو تحميل المحادثة
      emit(ChatLoaded(event.userId));
    } catch (e) {
      emit(ChatError(e.toString()));
    }
  }

  Future<void> _onLoadMessages(LoadMessages event, Emitter<ChatState> emit) async {
    emit(ChatLoading());
    try {
      await emit.forEach(
        _repository.getMessages(event.chatId),
        onData: (messages) => MessagesLoaded(messages),
        onError: (error, stackTrace) => ChatError(error.toString()),
      );
    } catch (e) {
      emit(ChatError(e.toString()));
    }
  }

  Future<void> _onSendMessage(SendMessage event, Emitter<ChatState> emit) async {
    try {
      final messageId = await _repository.sendMessage(event.message);
      final newMessage = event.message.copyWith(id: messageId);
      emit(MessageSent(newMessage));
    } catch (e) {
      emit(ChatError(e.toString()));
    }
  }
}
