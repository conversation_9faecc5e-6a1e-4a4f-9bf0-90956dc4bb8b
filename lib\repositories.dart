// ملف المستودعات (Repositories) - يحتوي على جميع المستودعات المستخدمة في التطبيق

import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:uuid/uuid.dart';
import 'models.dart';

// ======================================================
// مستودع المستخدمين (User Repository)
// ======================================================

class UserRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // الحصول على بيانات المستخدم
  Future<UserModel?> getUser(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        return UserModel.fromJson({
          'id': doc.id,
          ...doc.data()!,
        });
      }
      return null;
    } catch (e) {
      throw Exception('فشل الحصول على بيانات المستخدم: $e');
    }
  }

  // تحديث بيانات المستخدم
  Future<void> updateUser(UserModel user) async {
    try {
      await _firestore.collection('users').doc(user.id).update(user.toJson());
    } catch (e) {
      throw Exception('فشل تحديث بيانات المستخدم: $e');
    }
  }

  // البحث عن المستخدمين
  Future<List<UserModel>> searchUsers(String query) async {
    try {
      final snapshot = await _firestore
          .collection('users')
          .where('name', isGreaterThanOrEqualTo: query)
          .where('name', isLessThanOrEqualTo: query + '\uf8ff')
          .get();

      return snapshot.docs.map((doc) {
        return UserModel.fromJson({
          'id': doc.id,
          ...doc.data(),
        });
      }).toList();
    } catch (e) {
      throw Exception('فشل البحث عن المستخدمين: $e');
    }
  }

  // رفع صورة الملف الشخصي
  Future<String> uploadProfileImage(String userId, File imageFile) async {
    try {
      final storageRef = _storage.ref().child('profile_images/$userId');
      final uploadTask = storageRef.putFile(imageFile);
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw Exception('فشل رفع صورة الملف الشخصي: $e');
    }
  }
}

// ======================================================
// مستودع أنظمة الري (Irrigation System Repository)
// ======================================================

class IrrigationSystemRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // الحصول على جميع أنظمة الري للمستخدم
  Stream<List<IrrigationSystemModel>> getIrrigationSystems(String userId) {
    return _firestore
        .collection('irrigation_systems')
        .where('userId', isEqualTo: userId)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return IrrigationSystemModel.fromJson({
          'id': doc.id,
          ...doc.data(),
        });
      }).toList();
    });
  }

  // الحصول على نظام ري محدد
  Stream<IrrigationSystemModel> getIrrigationSystem(String systemId) {
    return _firestore
        .collection('irrigation_systems')
        .doc(systemId)
        .snapshots()
        .map((doc) {
      return IrrigationSystemModel.fromJson({
        'id': doc.id,
        ...doc.data()!,
      });
    });
  }

  // إضافة نظام ري جديد
  Future<String> addIrrigationSystem(IrrigationSystemModel system) async {
    try {
      final docRef = await _firestore.collection('irrigation_systems').add(system.toJson());
      return docRef.id;
    } catch (e) {
      throw Exception('فشل إضافة نظام الري: $e');
    }
  }

  // تحديث نظام ري
  Future<void> updateIrrigationSystem(IrrigationSystemModel system) async {
    try {
      await _firestore.collection('irrigation_systems').doc(system.id).update(system.toJson());
    } catch (e) {
      throw Exception('فشل تحديث نظام الري: $e');
    }
  }

  // حذف نظام ري
  Future<void> deleteIrrigationSystem(String systemId) async {
    try {
      await _firestore.collection('irrigation_systems').doc(systemId).delete();
    } catch (e) {
      throw Exception('فشل حذف نظام الري: $e');
    }
  }
}

// ======================================================
// مستودع تشخيص الأمراض (Disease Diagnosis Repository)
// ======================================================

class DiseaseDiagnosisRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // الحصول على سجل التشخيصات للمستخدم
  Stream<List<DiseaseDiagnosisModel>> getDiagnosisHistory(String userId) {
    return _firestore
        .collection('disease_diagnoses')
        .where('userId', isEqualTo: userId)
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return DiseaseDiagnosisModel.fromJson({
          'id': doc.id,
          ...doc.data(),
        });
      }).toList();
    });
  }

  // حفظ نتيجة التشخيص
  Future<String> saveDiagnosis(DiseaseDiagnosisModel diagnosis) async {
    try {
      final docRef = await _firestore.collection('disease_diagnoses').add(diagnosis.toJson());
      return docRef.id;
    } catch (e) {
      throw Exception('فشل حفظ نتيجة التشخيص: $e');
    }
  }

  // رفع صورة النبات
  Future<String> uploadPlantImage(File imageFile) async {
    try {
      final uuid = const Uuid();
      final imageId = uuid.v4();
      final storageRef = _storage.ref().child('plant_images/$imageId');
      final uploadTask = storageRef.putFile(imageFile);
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw Exception('فشل رفع صورة النبات: $e');
    }
  }
}

// ======================================================
// مستودع السوق (Market Repository)
// ======================================================

class MarketRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // الحصول على جميع المنتجات
  Stream<List<ProductModel>> getProducts({String? category, String? searchQuery}) {
    Query query = _firestore.collection('products').orderBy('timestamp', descending: true);
    
    if (category != null && category.isNotEmpty && category != 'الكل') {
      query = query.where('category', isEqualTo: category);
    }
    
    return query.snapshots().map((snapshot) {
      List<ProductModel> products = snapshot.docs.map((doc) {
        return ProductModel.fromJson({
          'id': doc.id,
          ...doc.data() as Map<String, dynamic>,
        });
      }).toList();
      
      // تصفية المنتجات حسب البحث إذا تم توفير استعلام البحث
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final searchLower = searchQuery.toLowerCase();
        products = products.where((product) {
          return product.name.toLowerCase().contains(searchLower) ||
              (product.description?.toLowerCase().contains(searchLower) ?? false);
        }).toList();
      }
      
      return products;
    });
  }

  // الحصول على منتج محدد
  Future<ProductModel> getProduct(String productId) async {
    try {
      final doc = await _firestore.collection('products').doc(productId).get();
      if (doc.exists) {
        return ProductModel.fromJson({
          'id': doc.id,
          ...doc.data()!,
        });
      } else {
        throw Exception('المنتج غير موجود');
      }
    } catch (e) {
      throw Exception('فشل الحصول على المنتج: $e');
    }
  }

  // إضافة منتج جديد
  Future<String> addProduct(ProductModel product) async {
    try {
      final docRef = await _firestore.collection('products').add(product.toJson());
      return docRef.id;
    } catch (e) {
      throw Exception('فشل إضافة المنتج: $e');
    }
  }

  // تحديث منتج
  Future<void> updateProduct(ProductModel product) async {
    try {
      await _firestore.collection('products').doc(product.id).update(product.toJson());
    } catch (e) {
      throw Exception('فشل تحديث المنتج: $e');
    }
  }

  // حذف منتج
  Future<void> deleteProduct(String productId) async {
    try {
      await _firestore.collection('products').doc(productId).delete();
    } catch (e) {
      throw Exception('فشل حذف المنتج: $e');
    }
  }

  // رفع صورة المنتج
  Future<String> uploadProductImage(File imageFile) async {
    try {
      final uuid = const Uuid();
      final imageId = uuid.v4();
      final storageRef = _storage.ref().child('product_images/$imageId');
      final uploadTask = storageRef.putFile(imageFile);
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      throw Exception('فشل رفع صورة المنتج: $e');
    }
  }
}

// ======================================================
// مستودع المحادثات (Chat Repository)
// ======================================================

class ChatRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // الحصول على جميع المحادثات للمستخدم
  Stream<List<ChatModel>> getChats(String userId) {
    return _firestore
        .collection('chats')
        .where('participants', arrayContains: userId)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return ChatModel.fromJson({
          'id': doc.id,
          ...doc.data(),
        });
      }).toList();
    });
  }

  // إنشاء محادثة جديدة
  Future<String> createChat(ChatModel chat) async {
    try {
      final docRef = await _firestore.collection('chats').add(chat.toJson());
      return docRef.id;
    } catch (e) {
      throw Exception('فشل إنشاء المحادثة: $e');
    }
  }

  // الحصول على رسائل محادثة
  Stream<List<MessageModel>> getMessages(String chatId) {
    return _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return MessageModel.fromJson({
          'id': doc.id,
          ...doc.data(),
        });
      }).toList();
    });
  }

  // إرسال رسالة
  Future<String> sendMessage(MessageModel message) async {
    try {
      final docRef = await _firestore
          .collection('chats')
          .doc(message.chatId)
          .collection('messages')
          .add(message.toJson());
      
      // تحديث آخر رسالة في المحادثة
      await _firestore.collection('chats').doc(message.chatId).update({
        'lastMessage': message.text,
        'lastMessageTimestamp': message.timestamp,
      });
      
      return docRef.id;
    } catch (e) {
      throw Exception('فشل إرسال الرسالة: $e');
    }
  }
}
